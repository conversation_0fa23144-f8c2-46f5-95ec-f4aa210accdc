#!/usr/bin/python3

"""
记录创建追踪工具
用于监控和分析飞书多维表格中记录的创建和更新操作
"""

import lark_app_table
import config
from log_config import logger
import time
from datetime import datetime

def track_record_operations():
    """追踪记录操作"""
    logger.info("Starting record operations tracking...")
    
    # 初始化TVAS表格对象
    tvas_main_table = lark_app_table.TVAS()
    
    try:
        # 获取当前所有记录
        logger.info("Getting current records...")
        all_records = tvas_main_table.list_remote_records(None)
        if not all_records or not all_records.get("items"):
            logger.error("Failed to get records or no records found.")
            return
        
        current_records = {}
        for item in all_records["items"]:
            work_order_id = item.get("fields", {}).get("工单ID")
            if work_order_id:
                if work_order_id not in current_records:
                    current_records[work_order_id] = []
                current_records[work_order_id].append({
                    "record_id": item["record_id"],
                    "created_time": item.get("fields", {}).get("需求创建时间", 0)
                })
        
        logger.info(f"Current state: {len(all_records['items'])} total records")
        logger.info(f"Unique work orders: {len(current_records)}")
        
        # 检查重复记录
        duplicates = {wid: records for wid, records in current_records.items() if len(records) > 1}
        if duplicates:
            logger.warning(f"Found {len(duplicates)} work orders with duplicate records:")
            for wid, records in duplicates.items():
                logger.warning(f"  Work Order {wid}: {len(records)} records")
                for i, record in enumerate(records):
                    created_time = datetime.fromtimestamp(int(str(record['created_time'])[0:10])) if record['created_time'] else "Unknown"
                    logger.warning(f"    Record {i+1}: {record['record_id']} (Created: {created_time})")
        else:
            logger.info("✓ No duplicate records found.")
        
        return current_records, duplicates
        
    except Exception as e:
        logger.error(f"Error during tracking: {e}")
        return None, None

def compare_before_after(before_records, after_records):
    """比较操作前后的记录变化"""
    if not before_records or not after_records:
        logger.error("Cannot compare - missing record data")
        return
    
    logger.info("Analyzing record changes...")
    
    # 检查新增的工单
    new_work_orders = set(after_records.keys()) - set(before_records.keys())
    if new_work_orders:
        logger.info(f"New work orders created: {len(new_work_orders)}")
        for wid in new_work_orders:
            records = after_records[wid]
            logger.info(f"  {wid}: {len(records)} record(s)")
            if len(records) > 1:
                logger.warning(f"    ⚠️  Multiple records created for new work order!")
    
    # 检查记录数量变化
    for wid in before_records.keys():
        if wid in after_records:
            before_count = len(before_records[wid])
            after_count = len(after_records[wid])
            if after_count > before_count:
                logger.warning(f"Work order {wid}: record count increased from {before_count} to {after_count}")
                # 找出新增的记录
                before_ids = {r["record_id"] for r in before_records[wid]}
                after_ids = {r["record_id"] for r in after_records[wid]}
                new_ids = after_ids - before_ids
                logger.warning(f"  New record IDs: {new_ids}")

def main():
    """主函数"""
    print("Record Creation Tracking Tool")
    print("=" * 50)
    
    # 获取操作前的状态
    logger.info("Getting initial state...")
    before_records, before_duplicates = track_record_operations()
    
    if before_records is None:
        print("Failed to get initial state.")
        return
    
    print(f"\nInitial state:")
    print(f"  Total records: {sum(len(records) for records in before_records.values())}")
    print(f"  Unique work orders: {len(before_records)}")
    print(f"  Work orders with duplicates: {len(before_duplicates) if before_duplicates else 0}")
    
    # 等待用户操作
    input("\nPress Enter after running the main program to check for changes...")
    
    # 获取操作后的状态
    logger.info("Getting final state...")
    after_records, after_duplicates = track_record_operations()
    
    if after_records is None:
        print("Failed to get final state.")
        return
    
    print(f"\nFinal state:")
    print(f"  Total records: {sum(len(records) for records in after_records.values())}")
    print(f"  Unique work orders: {len(after_records)}")
    print(f"  Work orders with duplicates: {len(after_duplicates) if after_duplicates else 0}")
    
    # 比较变化
    compare_before_after(before_records, after_records)
    
    print("\nTracking completed.")

if __name__ == "__main__":
    main()
