import time
import json
import requests
from typing import Optional

from log_config import logger
import config


class CreateDemand:
    def __init__(self, cookie: dict):
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36",
            "Content-Type": "application/json"
        }

        # 车辆平台列表
        self.platform_list = self.get_platform_list()

    def create_demand(self, demand: dict) -> Optional[list]:
        logger.info(f"Create demand.")
        data_list = self.load_data(demand)
        if data_list is None or len(data_list) == 0:
            logger.error("Failed to construct demand_dict from feishu records, skip create demand!")
            return None

        demand_ids = []
        for data_dict in data_list:
            logger.debug(data_dict)
            self.payload = json.dumps(data_dict)
            url = "https://request-proxy.nioint.com/proxy/tcr/v1/demand/batch_save"
            response = requests.request("POST", url, headers=self.headers, data=self.payload)

            if response.ok and response.status_code == 200:
                response_dict = response.json()
                if response_dict.get("resultCode") == "success":
                    data_list = response_dict.get("data")
                    if len(data_list) != 0:
                        data_dict = data_list[0]
                        if data_dict and data_dict.get("data", {}).get("id"):
                            demand_ids.append(data_dict.get("data", {}).get("id"))
                    else:
                        logger.warning(response.text)
                else:
                    logger.warning(response.text)
            else:
                logger.warning(response.text)

        if len(demand_ids) == 0:
            logger.error(f"Failed to create demand for {demand}!")
            return None
        else:
            return demand_ids

    def get_platform_list(self):
        url = "https://request-proxy.nioint.com/proxy/tcr/v1/resource-life-cycle/getListPrograms"
        response = requests.request("GET", url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_list = response_dict.get("data")
            if data_list is not None and len(data_list) > 0:
                return data_list

        logger.error("Failed to get platform list!")
        logger.warning(response_dict)
        return None

    def get_vehicle_platform(self, vehicle_model: str, vehicle_year: str):
        if self.platform_list is None:
            return None

        for i in self.platform_list:
            if i.get("vehicleModel", "") == vehicle_model:
                year_nt_list = i.get("yearAndNtList", [])
                for j in year_nt_list:
                    if j.get("vehicleYear", "") == vehicle_year:
                        return j.get("nt", [])[0]

        logger.error(f"Failed to get platform for {vehicle_model} {vehicle_year}!")
        return None

    def query_by_vin(self, vin: str):
        url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin?vin={vin}"
        response = requests.request("GET", url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_list = response_dict.get("data")
            if data_list is None or len(data_list) == 0:
                logger.error(f"No vehicle info found for {vin}, please check that the vin is correct!")
                return None
            else:
                data_dict = data_list[0]
                return data_dict
        else:
            logger.error(f"Failed to query vehicle info by vin {vin}!")
            logger.warning(response_dict)
            return None

    def load_data(self, record):
        demand_type_list = []
        # 确认工单类型
        for i in record.get("工单类型"):
            demand_type = config.DEMAND_TYPE.get(i)
            if demand_type is None:
                logger.warning(f"Demand with {i} type is not supported!")
                continue
            demand_type_list.append(demand_type)

        if len(demand_type_list) == 0:
            logger.error(f"Failed to get work demand type for {record}!")
            return None

        # 软件刷写 硬件车辆运维 公共的属性
        # 对应TVAS系统”交付地点“ 飞书“改装需求池管理”多维表格”改制区域“
        to_site_code = {
            "software_flash":
                {
                    "上海": "DP00068",  # 上海安驰路
                    "北京": "DP00113",  # 北京比目鱼创业园
                    "合肥": "DP00135"  # AD-合肥蔚来中国总部
                },
            "car_operations":
                {
                    "上海": "DP00068",  # 上海安驰路
                    "北京": "DP00064",  # 北京
                    "合肥": "DP00087"  # 合肥徽都科技园
                }
        }
        software_site = to_site_code.get("software_flash").get(record.get("改制区域"))
        hardware_site = to_site_code.get("car_operations").get(record.get("改制区域"))

        if not (software_site or hardware_site):
            logger.error(f"Failed to get work demand type for {record}!")
            return None

        # 需求车辆列表
        demand_vehicle_list_software = []
        demand_vehicle_list_hardware = []
        vins = record.get("VIN")
        if vins is None:
            logger.error("Failed to get demand vehicles!")
            return None
        vin_list = vins.split()
        for vin in vin_list:
            # 检查输入的 VIN 是否正确，不正确则跳过
            response_dict = self.query_by_vin(vin)
            if response_dict is None:
                continue

            vehicle_model = response_dict.get("vehicle_model", "")
            vehicle_generation = response_dict.get("vehicle_generation", "")
            project_code = vehicle_model + " " + vehicle_generation
            software_platform = self.get_vehicle_platform(vehicle_model, vehicle_generation)
            vehicle_info_software = {
                "vin": vin,
                "vinType": "vin",
                "projectCode": project_code,
                "softwarePlatform": software_platform,
                "implementSite": software_site
            }
            vehicle_info_hardware = {
                "vin": vin,
                "vinType": "vin",
                "projectCode": project_code,
                "softwarePlatform": software_platform,
                "implementSite": hardware_site
            }
            demand_vehicle_list_software.append(vehicle_info_software)
            demand_vehicle_list_hardware.append(vehicle_info_hardware)

        if len(demand_vehicle_list_software) == 0 and len(demand_vehicle_list_hardware) == 0:
            logger.error("Failed to get demand vehicles!")
            return None

        # 实际需求方
        demand_side = ""
        for i in record.get("需求方", [{}]):
            demand_side += i.get("name", "") + " (" + i.get("email", "") + ") "

        # 跟进人
        executor = ""
        for i in record.get("跟进人", [{}]):
            executor += i.get("name", "") + " (" + i.get("email", "") + ") "

        # 备注
        remark = "\n备注：" + record.get("备注", "")

        expect_end = int(str(record.get("期望交付日期", 0))[0:10])

        # 软件刷写 独占的属性
        vehicle_contact = record.get("需求方", [{}])[0].get("email", "").split("@")[0]

        # 硬件车辆运维 独占的属性
        # 硬件改制描述
        location = record.get("改装内容及原因，车辆及钥匙位置", "")

        expect_start = int(time.time())

        data_list = []
        for demand_type in demand_type_list:
            if demand_type == config.DEMAND_TYPE_SOFTWARE:
                # 需求描述
                description = record.get("项目名称", "") + \
                              " 刷写模块及软件链接：" + record.get("刷写模块及软件链接", "") + "\n需求方：" + demand_side + remark
                for demand_vehicle_dict_software in demand_vehicle_list_software:
                    data_dict = {
                        # release version
                        "demandStatus": "pending",
                        "updateUser": "lixue.gao",
                        # debug version
                        # "demandStatus": "draft",
                        # "updateUser": "falcon.fan",
                        "demandType": demand_type,
                        "expectEnd": expect_end,
                        "description": description[0:500],
                        "attachments": [],
                        "ext": {
                            "flashType": [
                                "universal_ecu"
                            ],
                            "vehicleContact": vehicle_contact,
                            "location": "",
                            "list": [demand_vehicle_dict_software],
                        }
                    }
                    data_list.append(data_dict)
            elif demand_type == config.DEMAND_TYPE_HARDWARE:
                description = location + "\n需求方：" + demand_side + remark
                for demand_vehicle_dict_hardware in demand_vehicle_list_hardware:
                    data_dict = {
                        # release version
                        "demandStatus": "pending",
                        "updateUser": "lixue.gao",
                        # debug version
                        # "demandStatus": "draft",
                        # "updateUser": "falcon.fan",
                        "demandType": demand_type,
                        "expectStart": expect_start,
                        "expectEnd": expect_end,
                        "description": description[0:500],
                        "attachments": [],
                        "ext": {
                            "list": [demand_vehicle_dict_hardware],
                        }
                    }
                    data_list.append(data_dict)
            else:
                logger.error(f"Demand with {demand_type} type is not supported!")
                continue

        return data_list
