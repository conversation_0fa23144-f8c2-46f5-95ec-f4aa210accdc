import datetime
from dateutil.relativedelta import relativedelta
import json
import threading

import config
import create_demand
from log_config import logger
from lark_api_wrapper import Lark


def timeout(seconds):
    def decorator(func):
        def wrapper(*args, **kwargs):
            result = dict()

            def new_func():
                nonlocal result
                result = func(*args, **kwargs)

            t = threading.Thread(target=new_func)
            t.start()
            t.join(seconds)
            if t.is_alive():
                raise TimeoutError(Exception(f"Function timed out after {seconds} seconds"))

            return result

        return wrapper

    return decorator


def get_work_order_id(record_str: str):
    record_info = record_str.split()
    for i in range(len(record_info)):
        if "工单ID" in record_info[i]:
            work_order_id = record_info[i + 1].strip(",").strip("'")
            return work_order_id


def get_feishu_user_open_id(user_ids: str):
    user_id_list = user_ids.split(",")
    open_id_list = []
    unconverted_user_id_list = []
    for user_id in user_id_list:
        email = user_id + "@nio.com"
        feishu_api = Lark()
        feishu_api.get_user_open_id(email)
        if feishu_api.response_success():
            response_data = json.loads(feishu_api.get_response_data())
            open_id_dict = response_data.get("user_list", [])[0]
            open_id = open_id_dict.get("user_id")
            if open_id:
                id_dict = {"id": open_id}
                open_id_list.append(id_dict)
            else:
                unconverted_user_id_list.append(user_id)
                logger.warning(f"Failed to get feishu open_id for {user_id}!")
    if len(open_id_list) == 0:
        return None, unconverted_user_id_list
    else:
        return open_id_list, unconverted_user_id_list


def to_feishu_user_open_id(record: dict, role: str):
    if record.get(role) is None:
        record[role] = None
        return

    open_id_list, user_id_list = get_feishu_user_open_id(record.get(role))
    record[role] = open_id_list
    if len(user_id_list) and role in ["申请人", "服务工程师"]:
        role_text = role + "_文本"
        record[role_text] = ",".join(user_id_list)


def add_additional_keys(record: dict, demands_request):
    demand_id = record.get("需求ID")
    demand_detail = demands_request.get_demand_detail(demand_id)
    roles = ["申请人", "服务工程师"]
    if demand_detail:
        quality_inspector = demand_detail.get("serviceOrders", [])[0].get("ext", {}).get("qualityInspector")
        quality_user = demand_detail.get("serviceOrders", [])[0].get("qualityUser")
        if quality_inspector:
            record["质检工程师"] = quality_inspector
            roles.append("质检工程师")
        elif quality_user:
            record["质检工程师"] = quality_user
            roles.append("质检工程师")
        else:
            logger.warning(f"Failed to get qualityInspector for {demand_id}!")

        vehicle_contact = demand_detail.get("serviceOrders", [])[0].get("ext", {}).get("vehicleContact")
        if vehicle_contact:
            record["车辆联系人"] = vehicle_contact
            roles.append("车辆联系人")

        vehicle_location = demand_detail.get("serviceOrders", [])[0].get("ext", {}).get("location")
        if vehicle_location:
            record["车辆位置"] = vehicle_location

    threads = []
    for role in roles:
        threads.append(threading.Thread(target=to_feishu_user_open_id, args=(record, role)))
    for thread in threads:
        thread.start()
    for thread in threads:
        thread.join()


def get_site_name_by_number(site_number: str, location_list: list):
    if site_number is None:
        logger.warning(f"Failed to get site name for empty site number!")
        return None

    for location_dict in location_list:
        if location_dict["sitNumber"] == site_number:
            return location_dict["name"]

    logger.warning(f"Failed to get site name for site number {site_number}!")
    return None


def load_pending_demand(demand: dict, location_list: list):
    """ 工单示例
    demand_example = {
        'createTime': '1701661107',
        'updateTime': '1701661109',
        'createUser': 'guoqiang.pan.o',
        'updateUser': 'guoqiang.pan.o',
        'deleted': False,
        'id': '1020063',
        'demandType': 'car_operations',
        'demandStatus': 'pending',
        'summary': '630车辆再执行任务过程中，车辆扎胎，漏气严重，需要救援补胎',
        'description': '630车辆再执行任务过程中，车辆扎胎，漏气严重，需要救援补胎',
        'demandRelId': '',
        'expectStart': '1701662400',
        'expectEnd': '1701666000',
        'ext': {
            'softwarePlatform': 'NT2.0',
            'projectCode': 'Force G1.1',
            'implementSite': 'DP00068',
            'vinType': 'vin',
            'vin': 'LJ1EFAUU0MG084630'
        },
        'tier1DepartmentRefId': None,
        'tier2DepartmentRefId': None,
        'lastUpdateTime': '1701661108',
        'demandSource': 'pc',
        'vin': 'LJ1EFAUU0MG084630',
        'orderNumber': None
    }
    """

    demand_id = demand.get("id")
    if demand_id is None:
        logger.error(f"Failed to get demand id for {demand}!")
        return None

    if demand.get("demandType", "") == "software_flash":
        demand_type = "软件刷写"
    else:
        demand_type = "车辆运维"

    create_user, other_create_user = get_feishu_user_open_id(demand.get("createUser"))

    site_number = demand.get("ext", {}).get("implementSite")
    site_name = get_site_name_by_number(site_number, location_list)
    if site_name is None:
        return None

    record = {
        "id": demand_id,
        "需求类型": demand_type,
        "需求状态": "待确认",
        "VIN": demand.get("vin"),
        "描述": demand.get("description"),
        "期望完成时间": round(int(demand.get("expectEnd")) * 1000),
        "提交时间": round(int(demand.get("updateTime")) * 1000),
        "申请人": create_user,
        "交付地点": site_name,
    }
    return record


def add_field(record: dict, key: str, value):
    if value:
        record[key] = value
    else:
        logger.warning(f"Failed to get {key}!")


class LarkAppTable:
    def __init__(self, app, table):
        self.app = app
        self.table = table
        self.feishu_api = Lark(app_token=self.app, table_id=self.table)

    @timeout(20)
    def list_remote_records_handle(self, record_filter, page_token):
        logger.debug(f"List https://nio.feishu.cn/base/{self.app}?table={self.table} remote records.")
        self.feishu_api.list_record(record_filter, page_token)
        if self.feishu_api.response_success():
            logger.debug("List remote records, ok.")
            return json.loads(self.feishu_api.get_response_data())

        logger.error("Failed to list remote records!")
        return None

    def list_remote_records(self, record_filter, page_token=None):
        try:
            res = self.list_remote_records_handle(record_filter, page_token)
        except TimeoutError as e:
            logger.error(e)
            return None
        else:
            return res

    def delete_remote_record(self, record_id):
        logger.debug(f"Delete https://nio.feishu.cn/base/{self.app}?table={self.table} remote record.")
        self.feishu_api.delete_record(record_id)
        if self.feishu_api.response_success():
            logger.debug("Delete remote record, ok.")
            return True

        logger.error("Failed to delete remote record!")
        return False

    def create_remote_record(self, record: dict):
        work_order_id = record.get("工单ID", "Unknown")
        logger.info(f"Creating remote record for work order: {work_order_id}")
        logger.debug(f"Create https://nio.feishu.cn/base/{self.app}?table={self.table} remote record.")
        self.feishu_api.create_record(record)
        if self.feishu_api.response_success():
            logger.info(f"Successfully created remote record for work order: {work_order_id}")
            return True

        logger.error(f"Failed to create remote record for work order: {work_order_id}")
        logger.warning(record)
        return False

    def update_remote_record(self, record: dict, record_id):
        logger.debug(f"Update https://nio.feishu.cn/base/{self.app}?table={self.table} remote record.")
        self.feishu_api.update_record(record, record_id)
        response_result = self.feishu_api.response_success()

        if response_result is True:
            logger.debug("Update remote record, ok.")
            return True
        elif response_result == "RECORD_NOT_FOUND":
            logger.warning(f"Record {record_id} not found, returning special status.")
            # 返回特殊状态，让上层逻辑决定是否创建新记录
            return "RECORD_NOT_FOUND"
        else:
            logger.error("Failed to update remote record!")
            logger.warning(record)
            return False


class TVAS(LarkAppTable):
    def __init__(self):
        super().__init__(config.APP_TOKEN, config.TABLE_ID)

    def list_tvas_remote_records_by_work_order_id(self, work_order_id: str):
        record_filter = f"CurrentValue.[工单ID] = \"{work_order_id}\""
        logger.info(f"Get records with {work_order_id} from feishu.")
        response_data = self.list_remote_records(record_filter)
        if response_data is None:
            return None

        return response_data.get("items")

    def delete_blank_rows_for_remote_excel(self):
        print("\n" + "*" * 60 + "\n")
        logger.info("To delete blank rows for excel in the feishu.")
        record_items = self.list_tvas_remote_records_by_work_order_id("")
        if record_items:
            for item in record_items:
                try:
                    self.delete_remote_record(item["record_id"])
                except Exception as e:
                    logger.warning(f"Failed to delete record {item['record_id']}: {e}")
                    continue

    def cleanup_invalid_records(self):
        """清理无效的记录ID缓存"""
        logger.info("Starting cleanup of invalid records...")
        try:
            # 获取所有记录
            all_records = self.list_remote_records(None)
            if all_records and all_records.get("items"):
                valid_record_ids = {item["record_id"] for item in all_records["items"]}
                logger.info(f"Found {len(valid_record_ids)} valid records in the table.")
            else:
                logger.warning("No records found or failed to fetch records.")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def check_and_remove_duplicate_records(self):
        """检查并删除重复的工单记录"""
        logger.info("Checking for duplicate work order records...")
        try:
            all_records = self.list_remote_records(None)
            if not all_records or not all_records.get("items"):
                logger.info("No records found.")
                return

            work_order_groups = {}
            for item in all_records["items"]:
                work_order_id = item.get("fields", {}).get("工单ID")
                if work_order_id:
                    if work_order_id not in work_order_groups:
                        work_order_groups[work_order_id] = []
                    work_order_groups[work_order_id].append(item)

            duplicates_found = 0
            for work_order_id, records in work_order_groups.items():
                if len(records) > 1:
                    duplicates_found += 1
                    logger.warning(f"Found {len(records)} duplicate records for work order {work_order_id}")
                    # 保留第一个记录，删除其余的
                    for i, record in enumerate(records[1:], 1):
                        record_id = record["record_id"]
                        logger.info(f"Deleting duplicate record {i}: {record_id}")
                        try:
                            self.delete_remote_record(record_id)
                        except Exception as e:
                            logger.error(f"Failed to delete duplicate record {record_id}: {e}")

            if duplicates_found == 0:
                logger.info("No duplicate records found.")
            else:
                logger.info(f"Processed {duplicates_found} sets of duplicate records.")

        except Exception as e:
            logger.error(f"Error during duplicate check: {e}")

    def update_tvas_remote_record(self, record: dict, record_id=None):
        # 有 record_id 时，直接更新
        if record_id:
            result = self.update_remote_record(record, record_id)
            if result is True:
                # 更新成功
                return
            elif result == "RECORD_NOT_FOUND":
                # 记录不存在，创建新记录
                logger.warning(f"Record {record_id} not found, creating new record")
                self.create_tvas_remote_record(record, True)
                return
            else:
                # 其他错误，不创建新记录，避免重复
                logger.error(f"Failed to update record {record_id}, skipping creation to avoid duplicates")
                return

        # 没有 record_id 时，需要 list record 来获取
        work_order_id = record["工单ID"]
        record_items = self.list_tvas_remote_records_by_work_order_id(work_order_id)

        if record_items is None:
            logger.warning(f"Failed to get remote record with work order id: {work_order_id}, to create.")
            self.create_tvas_remote_record(record, True)
            return

        updated_record = False
        # 可能有多条记录
        for item in record_items:
            # 二次确认工单ID
            if item["fields"]["工单ID"] == work_order_id:
                record_id = item["record_id"]
                if updated_record:
                    logger.warning(f"To delete repeated record with work order id: {work_order_id}!")
                    self.delete_remote_record(record_id)
                else:
                    logger.info(f"Update {work_order_id} record.")
                    if self.update_remote_record(record, record_id):
                        updated_record = True
            else:
                logger.error(f"The item's work order id doesn't match {work_order_id}!")
                logger.error(f"It may be wrong to list records.")

    def create_tvas_remote_record(self, record: dict, unique=False):
        work_order_id = record["工单ID"]
        if not unique:
            record_items = self.list_tvas_remote_records_by_work_order_id(work_order_id)
            if record_items is None or len(record_items) == 0:
                unique = True
            else:
                # 记录已存在，直接更新第一个匹配的记录
                for item in record_items:
                    if item["fields"]["工单ID"] == work_order_id:
                        record_id = item["record_id"]
                        logger.warning(f"Work order id {work_order_id} record already exists, updating record {record_id}.")
                        result = self.update_remote_record(record, record_id)
                        if result is True:
                            return  # 更新成功，直接返回
                        elif result == "RECORD_NOT_FOUND":
                            # 记录不存在了，创建新记录
                            logger.warning(f"Record {record_id} not found during update, will create new record.")
                            unique = True
                            break
                        else:
                            # 更新失败，记录错误但不重复创建
                            logger.error(f"Failed to update existing record {record_id}, skipping creation to avoid duplicates.")
                            return
                if not unique:
                    return  # 如果找到了记录但没有设置unique=True，说明更新成功了

        if unique:
            logger.info(f"Create {work_order_id} record.")
            self.create_remote_record(record)

    def handle_records(self, record_set, tmp_record_dict, today_record_dict, work_order_ids, demands_request):
        logger.info(f"All work order id {work_order_ids}.")
        for i in record_set:
            work_order_id = get_work_order_id(i)
            tmp_record = tmp_record_dict[work_order_id]
            tmp_record_cp = tmp_record.copy()
            print()
            logger.info(f"Current work order id {work_order_id}.")
            add_additional_keys(tmp_record, demands_request)
            if work_order_id in work_order_ids:
                local_record = today_record_dict[work_order_id]
                logger.warning(f"Just download record: {tmp_record_cp}")
                logger.warning(f"Stored local record: {local_record}")
                self.update_tvas_remote_record(tmp_record)
            else:
                # 对于新记录，先检查是否已存在，避免重复创建
                logger.info(f"Processing new work order: {work_order_id}")
                existing_records = self.list_tvas_remote_records_by_work_order_id(work_order_id)
                if existing_records and len(existing_records) > 0:
                    logger.warning(f"Work order {work_order_id} already exists in remote table, updating instead of creating.")
                    # 记录已存在，更新第一个匹配的记录
                    for item in existing_records:
                        if item["fields"]["工单ID"] == work_order_id:
                            record_id = item["record_id"]
                            self.update_remote_record(tmp_record, record_id)
                            break
                else:
                    # 记录不存在，创建新记录
                    logger.info(f"Creating new record for work order: {work_order_id}")
                    self.create_tvas_remote_record(tmp_record, True)

    def get_remote_unfinished_records(self):
        record_filter = "AND(NOT(OR(CurrentValue.[工单状态] = \"已完成\", CurrentValue.[工单状态] = \"验收中\", CurrentValue.[工单状态] = \"已取消\")), " \
                        "CurrentValue.[需求创建时间] < TODAY())"
        response_data = self.list_remote_records(record_filter)
        if response_data:
            record_items = response_data.get("items")
        else:
            record_items = None

        return record_items

    def get_all_remote_records(self):
        has_more = True
        page_token = None
        total = 0
        total_item = []
        while has_more:
            logger.debug(f"At page_token: {page_token}")
            response_data = self.list_remote_records(None, page_token)
            if response_data:
                record_items = response_data.get("items")
                if record_items:
                    total_item += record_items
                    page_token = response_data.get("page_token")
                    has_more = response_data.get("has_more")
                    total = response_data.get("total")
                else:
                    logger.error(f"Records item is empty at page_token {page_token}!")
            else:
                logger.error(f"Failed to get records at page_token {page_token}!")

        if len(total_item) == total:
            logger.debug(f"Get all {total} records from feishu.")
        else:
            logger.error(f"Get {len(total_item)} records from feishu != total {total}!")

        return total_item

    def get_all_remote_work_order_id_and_record_id_dict(self):
        work_order_id_and_record_id = {}
        for record in self.get_all_remote_records():
            work_order_id = record.get("fields", {}).get("工单ID")
            record_id = record.get("record_id")
            if work_order_id and record_id:
                if work_order_id in work_order_id_and_record_id.keys():
                    self.delete_remote_record(record_id)
                work_order_id_and_record_id[work_order_id] = record_id
            else:
                logger.warning(record)
        return work_order_id_and_record_id

    def upload_records(self, tmp_record_dict, id_to_id_dict, demands_request, update_created_record):
        for work_order_id in tmp_record_dict.keys():
            logger.info(f"Current work order id {work_order_id}.")
            tmp_record = tmp_record_dict[work_order_id]
            if work_order_id in id_to_id_dict.keys():
                if update_created_record:
                    add_additional_keys(tmp_record, demands_request)
                    self.update_tvas_remote_record(tmp_record, id_to_id_dict.get(work_order_id))
            else:
                add_additional_keys(tmp_record, demands_request)
                self.create_tvas_remote_record(tmp_record, True)


class Signal(LarkAppTable):
    def __init__(self):
        super().__init__(config.APP_TOKEN, config.SIGNAL_TABLE_ID)

    def update_warning_trigger_time(self):
        print("\n" + "*" * 60 + "\n")
        logger.info("To update warning trigger time for heartbeat signal record.")

        trigger_time = datetime.datetime.now() + relativedelta(minutes=5)
        # 保证是 13 位的时间戳
        trigger_time_timestamp = int(str(trigger_time.timestamp())[0:10]) * 1000
        record_dict = {"下次触发报警时间": trigger_time_timestamp}

        response_data = self.list_remote_records(None)
        if response_data:
            record_items = response_data.get("items")
        else:
            record_items = None

        if record_items is None:
            logger.info(f"Create heartbeat signal record with {trigger_time}.")
            if self.create_remote_record(record_dict):
                return True
            else:
                return False

        updated_first_item = False
        # 可能有多条记录
        for item in record_items:
            record_id = item["record_id"]
            if updated_first_item:
                if not self.delete_remote_record(record_id):
                    return False
            else:
                logger.info(f"Update heartbeat signal record to {trigger_time}.")
                if self.update_remote_record(record_dict, record_id):
                    updated_first_item = True
                else:
                    return False

        return True


class Pending(LarkAppTable):
    def __init__(self):
        super().__init__(config.APP_TOKEN, config.PENDING_DEMANDS_TABLE_ID)

    def sync_pending_demands_to_feishu(self, demands: list, location_list: list):
        # 当前需求记录
        local_records = {}
        for i in demands:
            record = load_pending_demand(i, location_list)
            if record is None:
                continue
            local_records[record.get("id")] = record

        # 获取飞书上的记录 作为远端需求记录
        response_data = self.list_remote_records(None)

        if response_data is None:
            # 说明获取远端记录失败了，不知道远端有多少，跳过本次
            return

        record_items = response_data.get("items")
        remote_records_with_record_id = {}
        # 若 record_items is None 则 远端记录 remote_records_with_record_id 为空
        if record_items:
            for item in record_items:
                demand_id = item.get("fields", {}).get("id")
                if demand_id is None:
                    logger.warning(f"To delete remote record without demand id!")
                    self.delete_remote_record(item.get("record_id"))
                    continue
                remote_records_with_record_id[demand_id] = item

        # 处理每一条当前记录
        for demand_id in local_records.keys():
            local_record = local_records.get(demand_id)

            if demand_id in remote_records_with_record_id.keys():
                # 当前记录在远端列表里的需要更新
                # 如果内容相同则跳过
                remote_record_with_record_id = remote_records_with_record_id.get(demand_id, {})
                remote_record = remote_record_with_record_id.get("fields", {})

                # 去除飞书多维表格中人员除去id以外的其他值
                trim_keys = {"申请人"}
                for key in remote_record.keys():
                    if key in trim_keys:
                        tmp_list = [{"id": p_dict.get("id")} for p_dict in remote_record[key]]
                        remote_record[key] = tmp_list

                record_id = remote_record_with_record_id.get("record_id")

                if remote_record == local_record:
                    logger.debug(f"Local record {demand_id} is same as the remote one, don't need to update.")
                else:
                    if record_id is None:
                        logger.error(f"Failed to get record_id for {demand_id} from feishu!")
                        continue
                    logger.debug(f"Remote record: {remote_record}")
                    logger.debug(f"Local record: {local_record}")
                    logger.debug(f"Record id: {record_id}.")
                    self.update_remote_record(local_record, record_id)
            else:
                # 当前记录不在远端列表里的需要新建
                self.create_remote_record(local_record)

        # 删除在远端列表中，而不在本地列表里的需求
        for demand_id in remote_records_with_record_id.keys():
            if demand_id not in local_records.keys():
                record_id = remote_records_with_record_id.get(demand_id, {}).get("record_id")
                if record_id is None:
                    logger.error(f"Failed to get record_id for {demand_id} from feishu!")
                    continue
                logger.warning("To delete remote record which is not in local records!")
                self.delete_remote_record(record_id)

        # 检查本地与远端需求的数量是否相同
        response_data = self.list_remote_records(None)

        if response_data is None:
            # 说明获取远端记录失败了，不知道远端有多少，跳过本次检查
            return

        remote_demands_count = response_data.get("total")
        local_demands_count = len(demands)
        if local_demands_count == remote_demands_count:
            logger.info("Sync pending demands from TVAS to feishu, ok.")
        else:
            logger.error(f"Pending demands in TVAS count is {local_demands_count} != {remote_demands_count} in feishu!")


class Pool(LarkAppTable):
    def __init__(self):
        super().__init__(config.DEMAND_POOL_APP_TOKEN, config.DEMAND_POOL_TABLE_ID)

    def get_records_from_feishu(self):
        # 这里是 进展状态 不是 工单状态
        # 忽略30天前的需求
        record_filter = "AND(OR(CurrentValue.[工单类型].contains(\"软件刷写\"), " \
                        "CurrentValue.[工单类型].contains(\"硬件改制\")), " \
                        "CurrentValue.[下发时间] > TODAY()-30, " \
                        "CurrentValue.[进展状态] = \"进行中\") "
        response_data = self.list_remote_records(record_filter)
        if response_data:
            record_items = response_data.get("items")
        else:
            record_items = None

        return record_items

    def update_record_status_in_the_feishu(self, record: dict, demand_ids=None):
        # record 是改装需求池多维表格中的记录
        record_id = record.get("record_id")
        fields = record.get("fields")

        software_description = fields.get("刷写模块及软件链接", "")
        hardware_description = fields.get("改装内容及原因，车辆及钥匙位置", "")
        expect_end = fields.get("期望交付日期", 0)
        remark = fields.get("备注", "")

        logger.debug(f"Will update \"{software_description} {hardware_description}\" record status.")

        is_finished = False
        is_canceled = False

        # 用于收集所有匹配的需求ID，避免重复更新
        collected_demand_ids = set()
        update_count = 0

        if demand_ids:
            # 只要一个demand_id完成 则需求池中的需求完成
            for demand_id in demand_ids:
                record_filter = f"CurrentValue.[需求ID] = \"{demand_id}\""
                tvas_table = TVAS()
                response_data = tvas_table.list_remote_records(record_filter)

                if response_data:
                    record_items = response_data.get("items")
                else:
                    record_items = None

                # record_items 是TVAS数据分析多维表格中的记录
                if record_items is None:
                    logger.warning(f"There is no demand_id: {demand_id} record!")
                    is_finished = False
                    is_canceled = False
                    continue

                for i in record_items:
                    record_fields = i.get("fields", {})
                    record_status = record_fields.get("工单状态", "")

                    if record_status in ["已完成", "验收中"]:
                        logger.info(f"demand_id {demand_id} record is {record_status}.")
                        is_finished = True
                        break
                    elif record_status == "已取消":
                        logger.info(f"demand_id {demand_id} record is {record_status}.")
                        is_canceled = True
                        break
                    else:
                        is_finished = False
                        is_canceled = False

                if is_finished or is_canceled:
                    break
        else:
            # 一个记录里会有多辆车
            vins = fields.get("VIN")
            if vins is None:
                logger.error("Failed to get demand vehicles!")
                return False

            vin_list = vins.split()
            for vin in vin_list:
                record_filter = f"CurrentValue.[车辆VIN号] = \"{vin}\""
                tvas_table = TVAS()
                response_data = tvas_table.list_remote_records(record_filter)
                if response_data:
                    record_items = response_data.get("items")
                else:
                    record_items = None

                # record_items 是TVAS数据分析多维表格中的记录
                if record_items is None:
                    logger.warning(f"There is no {vin} record!")
                    is_finished = False
                    is_canceled = False
                    continue

                record_is_matched = False
                for i in record_items:
                    record_fields = i.get("fields", {})
                    record_description = record_fields.get("需求描述", "")
                    record_status = record_fields.get("工单状态", "")
                    record_expect_date = record_fields.get("期望完成时间", 0)

                    if software_description in record_description or hardware_description in record_description \
                            and expect_end == record_expect_date:
                        record_is_matched = True

                        # 收集需求ID，避免重复更新
                        demand_id = record_fields.get("需求id", "")
                        if demand_id:
                            collected_demand_ids.add(demand_id)

                        if record_status == "已完成":
                            logger.info(
                                f"{vin} \"{record_description}\" record is matched, and the record is {record_status}.")
                            is_finished = True
                            break
                        elif record_status == "已取消":
                            logger.info(
                                f"{vin} \"{record_description}\" record is matched, and the record is {record_status}.")
                            is_canceled = True
                            break
                        else:
                            logger.debug(
                                f"{vin} \"{record_description}\" record is matched, but the record is {record_status}.")
                            is_finished = False
                            is_canceled = False
                
                # 只要tvas其中一个需求完成 则需求池中的需求完成
                if is_finished or is_canceled:
                    break

                # 仅车辆的VIN相同，描述没有匹配的情况
                if not record_is_matched:
                    is_finished = False
                    is_canceled = False
                    logger.debug("None of the records match.")

        # 统一更新记录，避免重复更新
        if collected_demand_ids:
            demand_ids_str = ",".join(sorted(collected_demand_ids))
            new_remark = remark
            if " 需求id:" not in new_remark:
                new_remark += " 需求id:" + demand_ids_str
            else:
                # 如果已经有需求ID，则替换
                parts = new_remark.split(" 需求id:")
                new_remark = parts[0] + " 需求id:" + demand_ids_str

            new_record = {"备注": new_remark}
            logger.info(f"Updating remark with collected demand IDs: {demand_ids_str}")
            self.update_remote_record(new_record, record_id)

        if is_finished or is_canceled:
            new_record = {"进展状态": "工单已结束"}
            logger.info(f"Will update \"{software_description} {hardware_description}\" record status to 工单已结束.")
            self.update_remote_record(new_record, record_id)

    def create_demands_in_the_tvas(self, records: list, cookie_dict: dict):
        # 请求初始化
        create_demand_request = create_demand.CreateDemand(cookie_dict)

        for i in records:
            print()
            record_id = i.get("record_id")
            fields = i.get("fields")
            if fields is None:
                logger.error("Can't get record fields from feishu!")
                logger.debug(i)
                continue

            vin = fields.get("VIN")
            if vin is None:
                logger.error("Can't get vin from record fields!")
                logger.debug(fields)
                continue

            logger.info(f"For {vin}:")
            remark = fields.get("备注", "")
            if "需求已在TVAS中创建" in remark:
                logger.debug("This demand has been created in the TVAS, skip create.")
                recorded_demand_ids = None
                if "需求id:" in remark:
                    recorded_demand_ids = remark.split("需求id:")[-1].split(",")
                self.update_record_status_in_the_feishu(i, recorded_demand_ids)
                continue
            elif "在TVAS下单失败" in remark:
                logger.debug("This demand has been created in the TVAS, but failed, skip.")
                continue

            demand_ids = create_demand_request.create_demand(fields)
            if demand_ids:
                logger.info(f"Created demands {demand_ids} for {vin}.")
                # 在多维表格中加入“需求已在TVAS中创建”的标记
                record = {"备注": fields.get("备注", "") + " 需求已在TVAS中创建" + " 需求id:" + ",".join(demand_ids)}
                self.update_remote_record(record, record_id)
            else:
                logger.error(f"Failed to create demand to tvas for {vin}!")
                logger.debug(fields)
                # 在多维表格中加入“在TVAS下单失败”的标记
                record = {"备注": fields.get("备注", "") + " 在TVAS下单失败"}
                self.update_remote_record(record, record_id)
