#!/usr/bin/python3

"""
重复创建测试工具
专门用于检测和验证重复记录创建问题
"""

import lark_app_table
import config
from log_config import logger
import time
from datetime import datetime

def get_work_order_counts():
    """获取当前工单记录数量统计"""
    tvas_main_table = lark_app_table.TVAS()
    
    try:
        all_records = tvas_main_table.list_remote_records(None)
        if not all_records or not all_records.get("items"):
            return {}
        
        work_order_counts = {}
        for item in all_records["items"]:
            work_order_id = item.get("fields", {}).get("工单ID")
            if work_order_id:
                work_order_counts[work_order_id] = work_order_counts.get(work_order_id, 0) + 1
        
        return work_order_counts
    except Exception as e:
        logger.error(f"Error getting work order counts: {e}")
        return {}

def find_duplicates(work_order_counts):
    """找出重复的工单"""
    duplicates = {wid: count for wid, count in work_order_counts.items() if count > 1}
    return duplicates

def monitor_creation():
    """监控记录创建过程"""
    print("Duplicate Creation Monitor")
    print("=" * 50)
    
    # 获取初始状态
    print("Getting initial state...")
    initial_counts = get_work_order_counts()
    initial_duplicates = find_duplicates(initial_counts)
    
    print(f"Initial state:")
    print(f"  Total work orders: {len(initial_counts)}")
    print(f"  Total records: {sum(initial_counts.values())}")
    print(f"  Duplicates: {len(initial_duplicates)}")
    
    if initial_duplicates:
        print("  Existing duplicates:")
        for wid, count in initial_duplicates.items():
            print(f"    {wid}: {count} records")
    
    # 等待用户运行程序
    input("\nPress Enter after running one cycle of the main program...")
    
    # 获取最终状态
    print("\nGetting final state...")
    final_counts = get_work_order_counts()
    final_duplicates = find_duplicates(final_counts)
    
    print(f"Final state:")
    print(f"  Total work orders: {len(final_counts)}")
    print(f"  Total records: {sum(final_counts.values())}")
    print(f"  Duplicates: {len(final_duplicates)}")
    
    # 分析变化
    print("\nAnalyzing changes...")
    
    # 新增的工单
    new_work_orders = set(final_counts.keys()) - set(initial_counts.keys())
    if new_work_orders:
        print(f"New work orders: {len(new_work_orders)}")
        for wid in new_work_orders:
            count = final_counts[wid]
            if count > 1:
                print(f"  ⚠️  {wid}: {count} records (DUPLICATE!)")
            else:
                print(f"  ✓  {wid}: {count} record")
    
    # 记录数量增加的工单
    increased_work_orders = []
    for wid in initial_counts.keys():
        if wid in final_counts:
            if final_counts[wid] > initial_counts[wid]:
                increased_work_orders.append(wid)
                print(f"  ⚠️  {wid}: increased from {initial_counts[wid]} to {final_counts[wid]} records")
    
    # 新增的重复记录
    new_duplicates = set(final_duplicates.keys()) - set(initial_duplicates.keys())
    if new_duplicates:
        print(f"\n🚨 NEW DUPLICATES DETECTED: {len(new_duplicates)}")
        for wid in new_duplicates:
            print(f"  {wid}: {final_duplicates[wid]} records")
    
    # 总结
    total_new_records = sum(final_counts.values()) - sum(initial_counts.values())
    print(f"\nSummary:")
    print(f"  New records created: {total_new_records}")
    print(f"  New work orders: {len(new_work_orders)}")
    print(f"  New duplicates: {len(new_duplicates)}")
    
    if len(new_duplicates) > 0:
        print("\n❌ DUPLICATE CREATION DETECTED!")
        return False
    elif total_new_records > len(new_work_orders):
        print("\n⚠️  More records created than new work orders - possible duplicates!")
        return False
    else:
        print("\n✅ No duplicate creation detected.")
        return True

def quick_duplicate_check():
    """快速检查当前重复记录"""
    print("Quick Duplicate Check")
    print("=" * 30)
    
    counts = get_work_order_counts()
    duplicates = find_duplicates(counts)
    
    print(f"Total work orders: {len(counts)}")
    print(f"Total records: {sum(counts.values())}")
    print(f"Duplicates: {len(duplicates)}")
    
    if duplicates:
        print("\nDuplicate work orders:")
        for wid, count in sorted(duplicates.items()):
            print(f"  {wid}: {count} records")
        return False
    else:
        print("\n✅ No duplicates found.")
        return True

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        quick_duplicate_check()
    else:
        monitor_creation()

if __name__ == "__main__":
    main()
