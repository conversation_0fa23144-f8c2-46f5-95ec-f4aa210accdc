import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
from lark_oapi.api.contact.v3 import *
import time
import requests
from functools import wraps

import config
from log_config import logger


def retry_on_connection_error(max_retries=3, delay=2):
    """网络连接错误重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except (requests.exceptions.ConnectionError,
                        requests.exceptions.Timeout,
                        ConnectionResetError) as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Network error on attempt {attempt + 1}/{max_retries}: {e}")
                        logger.info(f"Retrying in {delay} seconds...")
                        time.sleep(delay * (attempt + 1))  # 递增延迟
                        continue
                    else:
                        logger.error(f"All {max_retries} attempts failed. Last error: {e}")
                        raise
                except Exception as e:
                    logger.error(f"Unexpected error: {e}")
                    raise
            return None
        return wrapper
    return decorator


# SDK 使用说明: https://github.com/larksuite/oapi-sdk-python#readme
class Lark:
    def __init__(self, app_token=None, table_id=None):
        # 创建client
        self.client = lark.Client.builder() \
            .app_id(config.APP_ID) \
            .app_secret(config.APP_SECRET) \
            .log_level(lark.LogLevel.WARNING) \
            .build()

        self.app_token = app_token
        self.table_id = table_id

        self.request = None
        self.response = None

    @retry_on_connection_error(max_retries=3, delay=2)
    def create_record(self, record: dict):
        # 构造请求对象
        self.request: CreateAppTableRecordRequest = CreateAppTableRecordRequest.builder() \
            .app_token(self.app_token) \
            .table_id(self.table_id) \
            .request_body(AppTableRecord.builder()
                          .fields(record)
                          .build()) \
            .build()

        # 发起请求
        self.response: CreateAppTableRecordResponse = self.client.bitable.v1.app_table_record. \
            create(self.request)

    @retry_on_connection_error(max_retries=3, delay=2)
    def list_record(self, record_filter: str, page_token: str):
        # 构造请求对象
        if record_filter is None:
            if page_token is None:
                self.request: ListAppTableRecordRequest = ListAppTableRecordRequest.builder() \
                    .app_token(self.app_token) \
                    .table_id(self.table_id) \
                    .page_size(500) \
                    .build()
            else:
                self.request: ListAppTableRecordRequest = ListAppTableRecordRequest.builder() \
                    .app_token(self.app_token) \
                    .table_id(self.table_id) \
                    .page_token(page_token) \
                    .page_size(500) \
                    .build()
        else:
            if page_token is None:
                self.request: ListAppTableRecordRequest = ListAppTableRecordRequest.builder() \
                    .app_token(self.app_token) \
                    .table_id(self.table_id) \
                    .filter(record_filter) \
                    .page_size(500) \
                    .build()
            else:
                self.request: ListAppTableRecordRequest = ListAppTableRecordRequest.builder() \
                    .app_token(self.app_token) \
                    .table_id(self.table_id) \
                    .filter(record_filter) \
                    .page_token(page_token) \
                    .page_size(500) \
                    .build()

        # 发起请求
        self.response: ListAppTableRecordResponse = self.client.bitable.v1.app_table_record. \
            list(self.request)

    def delete_record(self, record_id: str):
        # 构造请求对象
        self.request: DeleteAppTableRecordRequest = DeleteAppTableRecordRequest.builder() \
            .app_token(self.app_token) \
            .table_id(self.table_id) \
            .record_id(record_id) \
            .build()

        # 发起请求
        self.response: DeleteAppTableRecordResponse = self.client.bitable.v1.app_table_record. \
            delete(self.request)

    @retry_on_connection_error(max_retries=3, delay=2)
    def update_record(self, record: dict, record_id: str):
        # 构造请求对象
        self.request: UpdateAppTableRecordRequest = UpdateAppTableRecordRequest.builder() \
            .app_token(self.app_token) \
            .table_id(self.table_id) \
            .record_id(record_id) \
            .request_body(AppTableRecord.builder()
                          .fields(record)
                          .build()) \
            .build()

        # 发起请求
        self.response: UpdateAppTableRecordResponse = self.client.bitable.v1.app_table_record. \
            update(self.request)

    def get_user_open_id(self, email):
        # 构造请求对象
        self.request: BatchGetIdUserRequest = BatchGetIdUserRequest.builder() \
            .user_id_type("open_id") \
            .request_body(BatchGetIdUserRequestBody.builder()
                          .emails([email])
                          .build()) \
            .build()

        # 发起请求
        self.response: BatchGetIdUserResponse = self.client.contact.v3.user.batch_get_id(self.request)

    def response_success(self):
        response_type = str(type(self.response)).split(".")[-1].strip(">").strip("\'")
        # 处理失败返回
        if not self.response.success():
            error_code = self.response.code
            error_msg = self.response.msg
            log_id = self.response.get_log_id()

            # 特殊处理记录不存在的错误
            if error_code == 1254043:  # record not found
                logger.warning(
                    f"{response_type} - Record not found (code: {error_code}), "
                    f"msg: {error_msg}, log_id: {log_id}. This record may have been deleted.")
                return "RECORD_NOT_FOUND"
            else:
                logger.error(
                    f"{response_type} failed, "
                    f"code: {error_code}, msg: {error_msg}, log_id: {log_id}.")
                return False
        else:
            return True

    def get_response_data(self):
        response_str = lark.JSON.marshal(self.response.data, indent=4)
        return response_str
