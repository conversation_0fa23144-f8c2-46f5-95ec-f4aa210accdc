import openpyxl
import datetime


# 日期时间字符串 转成 13位 int 类型时间戳
def get_timestamp(datetime_str):
    if not datetime_str:
        return None
    if "--" in datetime_str:
        return None
    datetime_struct = datetime.datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
    timestamp = int(str(datetime_struct.timestamp())[0:10]) * 1000
    return timestamp


# 13位 int 类型时间戳 转成 日期时间字符串
def get_date_from_timestamp(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M:%S")


class ParseSheet:
    def __init__(self, path: str):
        self.path = path
        self.wb = openpyxl.load_workbook(path)
        self.sheet = self.wb["工单信息"]

    def get_work_order_id_index(self, header: str):
        first_row = self.sheet[1]
        index = 0
        for cell in first_row:
            if cell.value == header:
                return index
            index = index + 1

        if index == self.sheet.max_column:
            print(f"Can't get {header} index")
            return None

    def get_work_order_id_set(self):
        work_order_id_set = set()
        if self.sheet.max_row < 2:
            return work_order_id_set

        index = self.get_work_order_id_index("工单ID")
        if index is None:
            return work_order_id_set

        for row in list(self.sheet.rows)[1:]:
            work_order_id_set.add(row[index].value)
        return work_order_id_set

    def get_one_record_dict(self, row):
        record_dict = {}
        first_row = self.sheet[1]
        for i in range(len(row)):
            key = first_row[i].value
            if "时间" in key:
                value = get_timestamp(row[i].value)
            elif "(h)" in key:
                value_str = row[i].value
                if value_str and value_str != "--":
                    value = float(value_str)
                else:
                    value = None
            elif "ID" in key:
                value_str = str(row[i].value)
                value = str(value_str.split(".")[0])
            else:
                value = row[i].value
            if value == "" or value == "--":
                value = None
            record_dict[key] = value
        return record_dict

    def get_records(self):
        address_list = [
            "上海安驰路", "上海安驰路(技研)", "上海创新港",
            "AD-合肥蔚来中国总部", "合肥徽都科技园",
            "北京比目鱼创业园", "北京诚盈中心", "北京",
            "重庆", "其他",
        ]
        record_dict = {}
        record_set = set()
        if self.sheet.max_row < 2:
            return record_dict, record_set

        for row in list(self.sheet.rows)[1:]:
            record = self.get_one_record_dict(row)
            if record.get("交付地点", "") not in address_list:
                continue
            record_dict[record.get("工单ID", "")] = record
            record_set.add(str(record))
        return record_dict, record_set

    def get_header_list(self):
        row_list = []
        first_row = self.sheet[1]
        for i in first_row:
            row_list.append(i.value)
        return row_list

    def add_record_from_dict(self, record: dict):
        header = self.get_header_list()
        record_list = []
        for header in header:
            value = record.get(header)
            if "时间" in header:
                value = get_date_from_timestamp(value)
            record_list.append(value)
        self.sheet.append(record_list)
        self.wb.save(self.path)

    def creat_empty_workbook(self, dest_path):
        first_row_list = self.get_header_list()
        wb = openpyxl.Workbook()
        sheet = wb.active
        sheet.title = "工单信息"
        sheet.append(first_row_list)
        wb.save(dest_path)

    def delete_non_header_rows(self):
        self.sheet.delete_rows(2, self.sheet.max_row - 1)
        self.wb.save(self.path)

    def close_file(self):
        self.wb.close()
