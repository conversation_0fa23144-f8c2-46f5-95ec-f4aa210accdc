#!/usr/bin/python3

"""
重复记录检查和清理工具
用于检测和清理飞书多维表格中的重复工单记录
"""

import lark_app_table
import config
from log_config import logger

def main():
    """主函数：检查并清理重复记录"""
    logger.info("Starting duplicate record check and cleanup...")
    
    # 初始化TVAS表格对象
    tvas_main_table = lark_app_table.TVAS()
    
    try:
        # 检查并删除重复记录
        tvas_main_table.check_and_remove_duplicate_records()
        
        # 再次检查确认清理结果
        logger.info("Performing verification check...")
        all_records = tvas_main_table.list_remote_records(None)
        if all_records and all_records.get("items"):
            work_order_groups = {}
            for item in all_records["items"]:
                work_order_id = item.get("fields", {}).get("工单ID")
                if work_order_id:
                    if work_order_id not in work_order_groups:
                        work_order_groups[work_order_id] = 0
                    work_order_groups[work_order_id] += 1
            
            duplicates = {wid: count for wid, count in work_order_groups.items() if count > 1}
            if duplicates:
                logger.warning(f"Still found {len(duplicates)} work orders with duplicates:")
                for wid, count in duplicates.items():
                    logger.warning(f"  Work Order {wid}: {count} records")
            else:
                logger.info("✓ No duplicate records found after cleanup.")
                
            logger.info(f"Total records in table: {len(all_records['items'])}")
            logger.info(f"Unique work orders: {len(work_order_groups)}")
        
    except Exception as e:
        logger.error(f"Error during duplicate check: {e}")
        return False
    
    logger.info("Duplicate check and cleanup completed.")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✓ Duplicate check completed successfully.")
    else:
        print("✗ Duplicate check failed.")
