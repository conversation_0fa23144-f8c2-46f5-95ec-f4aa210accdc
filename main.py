#!/usr/bin/python3

import os
import platform
import stat
import time
import datetime
from dateutil.relativedelta import relativedelta

import lark_app_table
import login_nio
import parse_sheet
import config
from log_config import logger
import get_demands

user_path = os.path.expanduser("~")
download_dir = os.path.join(user_path, "Downloads")
tvas_dir = os.path.join(download_dir, config.TVAS_DIR)


def remove_file(path: str):
    # 在 Windows 环境下，新下载的文件会被 FortiClient 杀毒软件扫描
    # 造成文件被占用，不能立即删除
    # 间隔 1s 尝试 20 次删除
    attempts = 20
    err_msg = ""
    while attempts > 0:
        try:
            os.remove(path)
            logger.debug(f"Remove {path}")
            return True
        except Exception as e:
            err_msg = str(e)
            if "[WinError 5]" in err_msg or "拒绝访问" in err_msg:
                #  windows下取消只读
                os.chmod(path, stat.S_IWRITE)
                attempts -= 1
                time.sleep(1)
                continue
    logger.error(f"Failed to remove {path}, because: {err_msg}!")
    return False


def delete_tvas_files_in_download():
    logger.debug(f"Delete old TVAS工单明细[*].xlsx files.")
    for file in os.listdir(download_dir):
        file_path = os.path.join(download_dir, file)
        if "TVAS工单明细" in file and os.path.isfile(file_path):
            remove_file(file_path)


def download_excel(start_date: str, end_date: str, demand_id=None):
    if demand_id:
        res = get_demands_request.download_a_demand(demand_id)
    else:
        res = get_demands_request.get_service_demands(start_date, end_date)
    res.raise_for_status()
    file_name = "TVAS工单明细" + today + ".xlsx"
    file_path = os.path.join(download_dir, file_name)
    file = open(file_path, 'wb')
    for chunk in res.iter_content(100000):
        file.write(chunk)
    file.close()


def handle_download_dir():
    # 等待 TVAS工单明细 文件下载，最多等待 20s
    start_time = time.time()
    file_name = "TVAS工单明细" + today + ".xlsx"
    file_path = os.path.join(download_dir, file_name)
    while time.time() - start_time < 20:
        if os.path.exists(file_path) and os.path.isfile(file_path):
            break
        logger.debug(f"Download {file_path} waiting...")
        time.sleep(1)

    # 如果有其他的文件的
    for file in os.listdir(download_dir):
        file_path = os.path.join(download_dir, file)
        if "TVAS工单明细" in file and os.path.isfile(file_path):
            if today in file and " (" not in file:
                logger.info(f"Parse {file_path}")
                return file_path
            else:
                remove_file(file_path)
    return None


def scan_virus_waiting():
    logger.debug("Scanning new file for viruses on windows platform takes some time.")


def handle_tvas_dir():
    if not os.path.exists(tvas_dir):
        os.mkdir(tvas_dir)
        return None
    for file in os.listdir(tvas_dir):
        file_path = os.path.join(tvas_dir, file)
        if "TVAS工单明细" in file and today in file and os.path.isfile(file_path):
            logger.info(f"Parse {file_path}")
            return file_path
    return None


def get_user_id(peoples: list):
    user_id_list = []
    for people in peoples:
        user_id = people.get("email").split("@")[0]
        user_id_list.append(user_id)
    return ",".join(user_id_list)


def from_feishu_people_to_user_id(record: dict):
    roles = ["申请人", "服务工程师"]
    for role in roles:
        role_feishu_id = record.get(role)
        if role_feishu_id:
            record[role] = get_user_id(role_feishu_id)
        else:
            record[role] = record.get(role + "_文本")
    return record


def sync_local_today_file(path: str):
    logger.info("Sync remote records in the TVAS to local excel.")
    today_sheet = parse_sheet.ParseSheet(path)
    today_sheet.delete_non_header_rows()

    record_filter = f"CurrentValue.[需求创建时间] > TODAY() - 1"

    # 从飞书上获取当天的需求 尝试 3 次
    record_items = None
    for i in range(3):
        response_data = tvas_main_table.list_remote_records(record_filter)
        if response_data:
            record_items = response_data.get("items")
            if record_items:
                logger.info(f"Get {len(record_items)} records from feishu.")
                break

    if record_items is None:
        logger.error("Failed to get records from feishu.")
        return

    for item in record_items:
        record = item["fields"]
        today_sheet.add_record_from_dict(from_feishu_people_to_user_id(record))


def sync_today_records():
    tmp_file_path = handle_download_dir()
    if tmp_file_path is None:
        logger.error("Unable to get download file!")
        return

    if platform.system() == "Windows":
        scan_virus_waiting()
    tmp_sheet = parse_sheet.ParseSheet(tmp_file_path)
    tmp_record_dict, tmp_record_set = tmp_sheet.get_records()
    tmp_sheet.close_file()

    if len(tmp_record_set) == 0:
        logger.info("Skip empty excel.")
        return

    today_file_path = handle_tvas_dir()
    is_new_file = False
    if today_file_path is None:
        today_file_name = "TVAS工单明细" + today + ".xlsx"
        today_file_path = os.path.join(tvas_dir, today_file_name)
        tmp_sheet.creat_empty_workbook(today_file_path)
        sync_local_today_file(today_file_path)
        is_new_file = True

    today_sheet = parse_sheet.ParseSheet(today_file_path)
    today_record_dict, today_record_set = today_sheet.get_records()

    need_to_handle_records = tmp_record_set.difference(today_record_set)
    if len(need_to_handle_records) > 0:
        logger.info(f"Found {len(need_to_handle_records)} new records to process.")
        updated_work_order_ids = today_sheet.get_work_order_id_set()
        tvas_main_table.handle_records(need_to_handle_records, tmp_record_dict, today_record_dict,
                                       updated_work_order_ids, get_demands_request)
        # 暂时禁用第二次同步，避免任何可能的重复处理
        logger.info("Skipping second sync to prevent any duplicate processing issues.")
    else:
        logger.info("No new records to process.")


def handle_today_records():
    print("\n" + "*" * 60 + "\n")
    logger.info("To sync today records.")

    delete_tvas_files_in_download()
    yesterday_with_minus = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    download_excel(yesterday_with_minus, today_with_minus)
    sync_today_records()


def date_str(date_struct):
    return datetime.datetime.strftime(date_struct, "%Y-%m-%d")


def sync_unfinished_records_handle(remote_records: list):
    logger.warning(f"To handle {len(remote_records)} records.")
    for remote_record in remote_records:
        try:
            record_id = remote_record.get("record_id")
            if record_id is None:
                logger.warning(f"Field to get record_id for {remote_record}!")
                continue

            demand_id = remote_record.get("fields", {}).get("需求ID")
            if demand_id is None:
                logger.warning(f"Field to get demand_id for {remote_record}!")
                continue

            logger.info(f"To sync the demand {demand_id}")
            timestamp = remote_record.get("fields", {}).get("需求创建时间")
            datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
            download_excel(date_str(datetime_struct), date_str(datetime_struct), demand_id)
            tvas_sheet_file_path = handle_download_dir()

            if tvas_sheet_file_path is None:
                logger.error("Unable to get download file!")
                continue
            if platform.system() == "Windows":
                scan_virus_waiting()

            tvas_sheet = parse_sheet.ParseSheet(tvas_sheet_file_path)
            tmp_record_dict, tmp_record_set = tvas_sheet.get_records()
            tvas_sheet.close_file()

            if len(tmp_record_set) == 0:
                logger.info("Skip empty excel.")
                continue

            a_record_dict = list(tmp_record_dict.values())[0]
            lark_app_table.add_additional_keys(a_record_dict, get_demands_request)
            tvas_main_table.update_tvas_remote_record(a_record_dict, record_id)

        except Exception as e:
            logger.error(f"Error processing record {record_id}: {e}")
            logger.warning(f"Skipping record and continuing with next one.")
            continue


def sync_unfinished_records():
    print("\n" + "*" * 60 + "\n")
    logger.info("To sync unfinished records.")

    # 1. 从飞书上获取未完成的需求
    unfinished_records = tvas_main_table.get_remote_unfinished_records()
    if unfinished_records is None:
        logger.info("There is no unfinished record in the feishu.")
        return

    # 2. 过滤掉今天刚创建的记录，避免重复处理
    import datetime
    today_timestamp = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000

    filtered_records = []
    for record in unfinished_records:
        create_time = record.get("fields", {}).get("需求创建时间", 0)
        if create_time and create_time < today_timestamp:
            filtered_records.append(record)
        else:
            work_order_id = record.get("fields", {}).get("工单ID", "Unknown")
            logger.debug(f"Skipping today's record {work_order_id} to avoid duplicate processing.")

    logger.info(f"Filtered {len(unfinished_records)} unfinished records to {len(filtered_records)} (excluding today's records).")

    if len(filtered_records) == 0:
        logger.info("No unfinished records to process after filtering.")
        return

    # 3. 同步过滤后的未完成需求
    sync_unfinished_records_handle(filtered_records)


def get_pending_demands_from_tvas() -> list:
    data_dict = get_demands_request.get_pending_demands(1)

    if data_dict is None:
        return []

    rows = data_dict.get("rows", [])
    page_num = data_dict.get("pageNum")
    page_count = data_dict.get("pageCount")
    if page_num is None or page_count is None:
        logger.error("Failed to get page_num or page_count")
        # 不返回，去检测row的大小与需求总数对比 在决定返回内容
    else:
        num = int(page_num)
        count = int(page_count)
        while num != count:
            num += 1
            next_page_data_dict = get_demands_request.get_pending_demands(num)
            if next_page_data_dict is None:
                continue
            else:
                rows.extend(next_page_data_dict.get("rows", []))

    demand_count = data_dict.get("total")
    if demand_count is None:
        logger.error("Failed to get demand_count")
        return []
    else:
        if len(rows) != int(demand_count):
            logger.error(f"Filed to get all pending demands, get {len(rows)} not demand count {int(demand_count)}")
            return []
        else:
            logger.info(f"Get all pending demands, get {len(rows)} equal to demand count {int(demand_count)}")
            return rows


def sync_pending_demands_from_tvas_to_feishu():
    print("\n" + "*" * 60 + "\n")
    logger.info("To sync pending demands from TVAS to feishu")

    # 1. 从TVAS上获取待确认的需求
    demands = get_pending_demands_from_tvas()

    # 2. 上传待确认的需求到飞书
    tvas_pending_table.sync_pending_demands_to_feishu(demands, location_list)


def sync_demands_from_feishu_to_tvas():
    print("\n" + "*" * 60 + "\n")
    logger.info("To sync ongoing demands from feishu to TVAS")

    # 1. 从多维表格中获得记录
    records = demand_pool_table.get_records_from_feishu()
    if records is None:
        logger.info("There is no record in the feishu, it needs to be submitted to TVAS")
        return

    # 2. 为每个记录在 TVAS 上创建需求
    demand_pool_table.create_demands_in_the_tvas(records, cookie_dict)


def upload_records_within_a_month(work_order_id_and_record_id: dict, update_created_record: bool):
    tmp_file_path = handle_download_dir()
    if tmp_file_path is None:
        logger.error("Unable to get download file!")
        return

    if platform.system() == "Windows":
        scan_virus_waiting()
    tmp_sheet = parse_sheet.ParseSheet(tmp_file_path)
    tmp_record_dict, tmp_record_set = tmp_sheet.get_records()
    tmp_sheet.close_file()

    if len(tmp_record_set) == 0:
        logger.info("Skip empty excel.")
        return

    tvas_main_table.upload_records(tmp_record_dict, work_order_id_and_record_id,
                                   get_demands_request, update_created_record)


def upload_all_demand(update_created_record: bool):
    # start_date_str = "2024-05-01"
    # start_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d")
    start_date = datetime.datetime.today() - relativedelta(days=40)
    start_date_str = date_str(start_date)
    end_date = datetime.datetime.today() - relativedelta(days=1) - relativedelta(seconds=60)
    end_date_str = date_str(end_date)

    # 分割成每个7天的区间
    logger.info(f"Download excels from {start_date_str} to {end_date_str}.")
    date_pair_list = []
    tmp_end_date = start_date - relativedelta(days=1)
    while tmp_end_date < end_date:
        tmp_start_date = tmp_end_date + relativedelta(days=1)
        tmp_end_date = min(tmp_start_date + relativedelta(weeks=1), tmp_start_date + relativedelta(days=10))
        date_pair_list.append((date_str(tmp_start_date), date_str(min(tmp_end_date, end_date))))
    if len(date_pair_list) > 1:
        logger.debug(f"Each download can only be downloaded for 7 days, and it needs to be downloaded multiple times:")
        logger.debug(date_pair_list)

    # 列出当前有的 更新
    work_order_id_and_record_id_dict = tvas_main_table.get_all_remote_work_order_id_and_record_id_dict()

    # 更新记录
    for i in date_pair_list:
        print()
        delete_tvas_files_in_download()
        download_excel(i[0], i[1])
        upload_records_within_a_month(work_order_id_and_record_id_dict, update_created_record)
        logger.error(f"Sync records from {i[0]} to {i[1]}, ok.")


if __name__ == "__main__":
    url = "https://tvas.nioint.com/tvas/orderManagement"
    element_id = "container"
    login = login_nio.LoginNio(url, element_id)
    cookie_dict = login.get_cookie()

    get_demands_request = get_demands.GetDemands(cookie_dict)
    location_list = get_demands_request.location_query()

    tvas_main_table = lark_app_table.TVAS()
    tvas_signal_table = lark_app_table.Signal()
    tvas_pending_table = lark_app_table.Pending()
    demand_pool_table = lark_app_table.Pool()

    # 程序启动时检查并清理重复记录
    logger.info("Checking for duplicate records on startup...")
    try:
        tvas_main_table.check_and_remove_duplicate_records()
    except Exception as e:
        logger.error(f"Error during startup duplicate check: {e}")

    try:
        while True:
            today_with_minus = datetime.date.today().strftime("%Y-%m-%d")
            today = datetime.date.today().strftime("%Y%m%d")

            # 同步最近一个月 TVAS 上的记录至飞书多维表格
            if config.SYNC_MONTH:
                upload_all_demand(False)
                exit()

            try:
                # 更新“下次触发报警时间” 心跳信号
                tvas_signal_table.update_warning_trigger_time()
            except Exception as e:
                logger.error(f"Error updating heartbeat signal: {e}")

            try:
                # 删除多维表格中的空白行
                tvas_main_table.delete_blank_rows_for_remote_excel()
            except Exception as e:
                logger.error(f"Error deleting blank rows: {e}")

            try:
                # 处理今天的记录
                handle_today_records()
            except Exception as e:
                logger.error(f"Error handling today's records: {e}")

            try:
                # 处理未完成的记录
                sync_unfinished_records()
            except Exception as e:
                logger.error(f"Error syncing unfinished records: {e}")

            try:
                # 同步TVAS系统中待确认的需求到多维表格
                sync_pending_demands_from_tvas_to_feishu()
            except Exception as e:
                logger.error(f"Error syncing pending demands: {e}")

            try:
                # 同步多维表格中进行中的需求到TVAS系统
                sync_demands_from_feishu_to_tvas()
            except Exception as e:
                logger.error(f"Error syncing demands from feishu to tvas: {e}")

            logger.info("sleeping...\n")
            time.sleep(60)

    # Ctrl-C 结束程序
    except KeyboardInterrupt:
        logger.info("Close script")
