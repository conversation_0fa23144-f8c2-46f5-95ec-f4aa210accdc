import logging
import logging.handlers
import colorlog

import os

project_dir = os.path.dirname(os.path.abspath(__file__))
project_name = project_dir.split("/")[-1].split("\\")[-1]
# 获取logger对象
logger = logging.getLogger(project_name)

# 指定最低日志级别：（critical > error > warning > info > debug）
logger.setLevel(logging.DEBUG)

# 日志格化字符串
console_fmt = "%(log_color)s%(asctime)s %(filename)s line %(lineno)d: [%(levelname)s] %(message)s"
file_fmt = "%(asctime)s %(filename)s line %(lineno)d: [%(levelname)s] %(message)s"

# 控制台输出不同级别日志颜色设置
color_config = {
    "DEBUG": "cyan",
    "INFO": "green",
    "WARNING": "yellow",
    "ERROR": "red",
    "CRITICAL": "bold_red",
}

console_formatter = colorlog.ColoredFormatter(fmt=console_fmt, log_colors=color_config)
file_formatter = logging.Formatter(fmt=file_fmt)

# 输出到控制台
console_handler = logging.StreamHandler()
# 输出到文件
log_dir = os.path.join(project_dir, "log")
if not os.path.exists(log_dir):
    os.mkdir(log_dir)
file_name = os.path.join(log_dir, project_name + ".log")
file_handler = logging.handlers.TimedRotatingFileHandler(filename=file_name, when='D', interval=1, backupCount=7,
                                                         encoding="utf-8")

# 设置日志格式
console_handler.setFormatter(console_formatter)
file_handler.setFormatter(file_formatter)

# 处理器设置日志级别，不同处理器可各自设置级别，默认使用logger日志级别
console_handler.setLevel(logging.DEBUG)
file_handler.setLevel(logging.DEBUG)

# logger添加处理器
# 避免重复打印日志
if not logger.handlers:
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
