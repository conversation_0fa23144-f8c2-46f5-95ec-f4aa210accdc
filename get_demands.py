import json
import requests

from log_config import logger


class GetDemands:
    def __init__(self, cookie: dict):
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
        self.url = ""
        self.method = ""
        self.platform_list = self.__get_platform_list()

    def get_pending_demands(self, page_num: int):
        logger.info(f"To get the pageNum: {page_num} pending demands.")
        data_dict = {
            "pageNum": page_num, "pageSize": 20,
            "demandId": [], "demandType": [], "demandStatus": ["pending"],
            "createUser": [], "engineer": [], "vin": "",
            "demandSource": None, "orderField": None, "desc": True,
            "implementSiteList": [], "vehicleModelList": [], "departmentIdList": []
        }

        self.payload = json.dumps(data_dict)
        self.url = "https://request-proxy.nioint.com/proxy/tcr/v1/demand/paginate-all-extends"
        self.method = "POST"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_dict = response_dict.get("data")
            if data_dict is not None:
                task_count = len(data_dict.get("rows", []))
                if task_count > 0:
                    return data_dict
                else:
                    logger.debug("There is no data.")
                    logger.debug(data_dict)
                    return None

        logger.error("Failed to get demands from tvas!")
        logger.warning(response_dict)
        return None

    def get_service_demands(self, start_date: str, end_date: str):
        logger.info(f"To export service demands from {start_date} to {end_date}.")
        data_dict = {
            "pageNum": 1, "pageSize": 50, "orderNumber": [], "orderType": [],
            "demandId": [], "demandType": [], "serviceStatus": [], "createUser": [],
            "vin": "", "solutionUser": "", "createTimeFrom": start_date, "createTimeTo": end_date,
            "implementSiteList": ["DP00068", "DP00087", "DP00135", "DP00113", "DP00064", "DP00079", "DP00066",
                                  "DP00112", "DP00084"],
        }
        # address= {   
        #     "上海安驰路": "DP00068",
        #     "上海创新港": "DP00066",

        #     "AD-合肥蔚来中国总部": "DP00135",
        #     "合肥徽都科技园": "DP00087",

        #     "北京比目鱼创业园": "DP00113",
        #     "北京诚盈中心": "DP00112",

        #     "北京": "DP00064",
        #     "重庆": "DP00079",

        #     "其他": "DP00084"
        # }
        self.payload = json.dumps(data_dict)
        self.url = "https://request-proxy.nioint.com/proxy/tcr/v1/service-order/export/admin"
        self.method = "POST"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            logger.debug(f"Export service demands from {start_date} to {end_date}.")
            return response

        logger.error(f"Failed to export service demands from {start_date} to {end_date}!")
        return None

    def query_fuzzy_name(self, name: str):
        self.url = f"https://request-proxy.nioint.com/proxy/people/people/v1/employee/query?fuzzy_name={name}" \
                   f"&employee_status=Active"
        self.method = "GET"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_list = response_dict.get("data", {}).get("list")
            if data_list is None or len(data_list) == 0:
                logger.error(f"No employee info found for {name}, please check that the name is correct!")
                return None
            else:
                for data_dict in data_list:
                    if data_dict.get("worker_user_id") == name:
                        return data_dict.get("name")
        else:
            logger.error(f"Failed to query employee info by name {name}!")
            logger.warning(response_dict)
            return None

    def location_query(self):
        self.url = f"https://request-proxy.nioint.com/proxy/tcr/v1/location/location-query"
        self.method = "POST"
        data_dict = {"demandType": ["car_operations", "software_flash"]}
        self.payload = json.dumps(data_dict)

        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_dict = response_dict.get("data")
            if data_dict is not None:
                locations = []
                for key, location_list in data_dict.items():
                    locations.extend(location_list)

                location_set = set()
                for i in locations:
                    location_set.add(json.dumps(i))

                location_list = []
                for i in location_set:
                    location_list.append(json.loads(i))

                return location_list

        logger.error("Failed to get demand locations!")
        logger.warning(response_dict)
        return None

    def __get_platform_list(self):
        self.url = "https://request-proxy.nioint.com/proxy/tcr/v1/resource-life-cycle/getListPrograms"
        self.method = "GET"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_list = response_dict.get("data")
            if data_list is not None and len(data_list) > 0:
                return data_list

        logger.error("Failed to get platform list!")
        logger.warning(response_dict)
        return None

    def get_vehicle_platform(self, vehicle_model: str, vehicle_year: str):
        if self.platform_list is None:
            return None

        for i in self.platform_list:
            if i.get("vehicleModel", "") == vehicle_model:
                year_nt_list = i.get("yearAndNtList", [])
                for j in year_nt_list:
                    if j.get("vehicleYear", "") == vehicle_year:
                        return j.get("nt", [])[0]

        logger.error(f"Failed to get platform for {vehicle_model} {vehicle_year}!")
        return None

    def query_by_vin(self, vin: str):
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin?vin={vin}"
        self.method = "GET"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_list = response_dict.get("data")
            if data_list is None or len(data_list) == 0:
                logger.error(f"No vehicle info found for {vin}, please check that the vin is correct!")
                return None
            else:
                data_dict = data_list[0]
                return data_dict
        else:
            logger.error(f"Failed to query vehicle info by vin {vin}!")
            logger.warning(response_dict)
            return None

    def get_demand_detail(self, demand_id):
        self.url = f"https://request-proxy.nioint.com/proxy/tcr/v1/demand/detail?id={demand_id}"
        self.method = "GET"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response.ok and response.status_code == 200:
            data_dict = response_dict.get("data")
            if data_dict:
                return data_dict

        logger.error(f"Failed to get demand detail for {demand_id}!")
        logger.warning(response_dict)
        return None

    def download_a_demand(self, demand_id: str):
        logger.info(f"To download a demand {demand_id}.")
        data_dict = {
            "pageNum": 1, "pageSize": 50, "orderNumber": [], "orderType": [], "demandId": [demand_id], "demandType": [],
            "serviceStatus": [], "createUser": [], "vin": "", "solutionUser": "", "createTimeFrom": "",
            "createTimeTo": ""}

        self.payload = json.dumps(data_dict)
        self.url = "https://request-proxy.nioint.com/proxy/tcr/v1/service-order/export/admin"
        self.method = "POST"

        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            logger.debug(f"Download a demand {demand_id}.")
            return response

        logger.error(f"Failed to download a demand {demand_id}!")
        return None
