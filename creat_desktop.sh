#!/usr/bin/env bash

CURRENT_DIR="$(dirname "$(realpath "${BASH_SOURCE[0]}")")"

file_name="${CURRENT_DIR##*/}.desktop"
file_path="./$file_name"
if [ -f "$file_path" ]; then
    rm "$file_path"
fi

echo "[Desktop Entry]
Version=1.0
#此处为桌面上文件展示的名称
Name=${CURRENT_DIR##*/}
#下面是调用shell脚本、并添加执行脚本的绝对路径
Exec=$CURRENT_DIR/run.sh
Terminal=true
Type=Application" >> "$file_path"

chmod +x "$file_path"

sudo cp "$file_path" /usr/share/applications/"$file_name"

cp /usr/share/applications/"$file_name" ~/Desktop/

rm "$file_path"
